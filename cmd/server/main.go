package main

import (
	"log"
	"net/http"
	"os"
	"time"

	"code.ixdev.cn/cnix/cbdv/acme-relay/acmex"
)

func main() {
	// 基本配置
	cfg := acmex.DefaultConfig(
		getEnv("ACME_ACCOUNT_EMAIL", "<EMAIL>"),
		getEnv("ACME_DATA_DIR", "./data"),
	)
	cfg.CADirURL = os.Getenv("ACME_DIRECTORY_URL") // 为空则用默认生产目录

	mgr, err := acmex.NewManager(cfg)
	if err != nil {
		log.Fatalf("init manager: %v", err)
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/getcert", func(w http.ResponseWriter, r *http.Request) {
		serverName := r.URL.Query().Get("server_name")
		if serverName == "" {
			http.Error(w, "missing server_name", http.StatusBadRequest)
			return
		}

		// 生产里你可以校验来源（只允许内网/Caddy），或做域名白名单检查
		ctx := r.Context()
		fullchain, key, err := mgr.GetCertificate(ctx, serverName)
		if err != nil {
			log.Printf("[ERR] issue %s: %v", serverName, err)
			http.Error(w, "issue/renew failed", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/x-pem-file")
		w.WriteHeader(http.StatusOK)
		_, _ = w.Write(fullchain)
		_, _ = w.Write([]byte("\n"))
		_, _ = w.Write(key)
	})

	addr := getEnv("LISTEN_ADDR", "127.0.0.1:6060")
	srv := &http.Server{
		Addr:              addr,
		Handler:           mux,
		ReadHeaderTimeout: 10 * time.Second,
	}
	log.Printf("certbox listening on http://%s/getcert", addr)
	log.Fatal(srv.ListenAndServe())
}

func getEnv(k, def string) string {
	if v := os.Getenv(k); v != "" {
		return v
	}
	return def
}
