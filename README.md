# ACME Relay

- Generated by ChatGPT 5

# Prompt

所有反向代理都是Caddy服务器；他们支持以下指令

Get certificates by making an HTTP(S) request. The response must have a 200 status code and the body must contain a PEM
chain including the full certificate (with intermediates) as well as the private key.
```
get_certificate http <url>

    url is the fully-qualified URL to which to make the request. 
    It is strongly advised that this be a local endpoint for performance reasons. 
    
    The URL will be augmented with the following query string parameters:
    server_name: SNI value
    signature_schemes: comma-separated list of hex IDs of signature algorithms
    cipher_suites: comma-separated list of hex IDS of cipher suites
```
帮我编写一个程序，当 Caady 需要TLS证书时，使用 go-acme/lego 向 Tencent Cloud DNS 发送请求，修改DNS记录，以公国 ACME DNS-01 认证获得证书
