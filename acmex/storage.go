package acmex

import (
	"crypto/x509"
	"encoding/pem"
	"errors"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

const (
	DirCerts       = "certs"
	fileAccount    = "account.json"
	fileAccountKey = "account.key"
)

func certPaths(dataDir, domain string) (crtPath, keyPath string) {
	base := strings.ReplaceAll(domain, "*", "_wild")
	crtPath = filepath.Join(dataDir, DirCerts, base+".crt")
	keyPath = filepath.Join(dataDir, DirCerts, base+".key")
	return
}

func ensureDirs(dataDir string) error {
	if err := os.MkdirAll(filepath.Join(dataDir, DirCerts), 0700); err != nil {
		return err
	}
	return nil
}

func LoadCert(dataDir, domain string) (certPEM, keyPEM []byte, notAfter time.Time, err error) {
	if err = ensureDirs(dataDir); err != nil {
		return nil, nil, time.Time{}, err
	}
	crt, key := certPaths(dataDir, domain)

	certPEM, err = os.ReadFile(crt)
	if err != nil {
		return nil, nil, time.Time{}, err
	}
	keyPEM, err = os.ReadFile(key)
	if err != nil {
		return nil, nil, time.Time{}, err
	}

	// 解析 leaf 证书 NotAfter
	block, _ := pem.Decode(certPEM)
	if block == nil || block.Type != "CERTIFICATE" {
		return nil, nil, time.Time{}, errors.New("acmex: invalid cert pem")
	}
	cert, err := x509.ParseCertificate(block.Bytes)
	if err != nil {
		return nil, nil, time.Time{}, err
	}
	return certPEM, keyPEM, cert.NotAfter, nil
}

func SaveCert(dataDir, domain string, certPEM, keyPEM []byte) error {
	if err := ensureDirs(dataDir); err != nil {
		return err
	}
	crt, key := certPaths(dataDir, domain)

	if err := os.WriteFile(crt, certPEM, 0600); err != nil {
		return fmt.Errorf("acmex: write cert: %w", err)
	}
	if err := os.WriteFile(key, keyPEM, 0600); err != nil {
		return fmt.Errorf("acmex: write key: %w", err)
	}
	return nil
}
